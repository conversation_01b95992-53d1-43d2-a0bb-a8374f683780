import requests

url = "https://paiplusinference.alipay.com/inference/7a2f7401cee56342_smart_cover_enhancement/v1"
body = {"features":{},"tensorFeatures":{"data":{"shapes":[1],"stringValues":["{\"cover_id\": \"A*zbI-S4hAlU8AAAAAevAAAAgAerZ9AQ\", \"cover_url\": \"\", \"title\": \"芋泥的受众群体到底是谁\", \"cat\": \"美食\", \"video_name\": \"\", \"video_id\": \"\", \"contentId\": \"20250724OB020010033349094691\", \"opt_path\": \"super\", \"logs\": {}, \"version\": 1}"]}}}
headers = {
    "Content-Type": "application/json;charset=utf-8",
    "MPS-app-name": "your-app-name",
    "MPS-http-version": "1.0",
    "MPS-trace-id": "your-trace-id"
}

r = requests.post(url=url, json=body, headers=headers)
res = r.json()
print(res)