# -*- coding: utf-8 -*-
import os
import csv
import json
from pathlib import Path
from typing import Optional

from PIL import Image

from model import UserHandler
from io_utils import load_image, get_url_from_afts


RESULTS_DIR = Path("tests/results")
CSV_PATH = Path("tests/top2000.csv")


def ensure_results_dir():
    RESULTS_DIR.mkdir(parents=True, exist_ok=True)


def _save_image(img: Optional[Image.Image], path: Path) -> bool:
    if img is None:
        return False
    try:
        # Convert to RGB if needed for JPEG
        if img.mode not in ("RGB", "L"):
            img = img.convert("RGB")
        img.save(path)
        return True
    except Exception as e:
        print(f"Failed to save image to {path}: {e}")
        return False


def process_one(model: UserHandler, row: dict, idx: int):
    ctt_id = row.get("ctt_id", f"row{idx}")
    title = row.get("ctt_title", "")
    cate = row.get("cate4_level1_name", "")
    cover_id = row.get("cover_id", "")
    video_id = row.get("video_id", "")

    data = {
        "cover_id": cover_id,
        "cover_url": "",  # 让模型内部自行通过 AFTS 解析 URL
        "title": title,
        "cat": cate,
        "video_name": "",
        "video_id": video_id,
        "contentId": ctt_id,
        "opt_path": "super",
        "logs": {},
        "version": 1,
    }

    # 调用模型
    try:
        _, _, result_map = model.predict_np({"data": json.dumps(data, ensure_ascii=False).encode()}, trace_id=f"test_{ctt_id}")
        output = json.loads(result_map.get("output", "{}"))
    except Exception as e:
        print(f"[{idx:02d}/{ctt_id}] 模型调用失败: {e}")
        output = {"enhanced": False}

    # 保存原图
    try:
        orig_img = load_image(cover_id if cover_id else get_url_from_afts(cover_id))
        orig_path = RESULTS_DIR / f"{idx:02d}_{ctt_id}_orig.jpg"
        ok = _save_image(orig_img, orig_path)
        print(f"[{idx:02d}/{ctt_id}] 保存原图: {'OK' if ok else 'FAIL'} -> {orig_path}")
    except Exception as e:
        print(f"[{idx:02d}/{ctt_id}] 保存原图失败: {e}")

    # 保存增强后的图片（若有）
    try:
        enhanced_img = None
        enhanced_cover_id = output.get("new_cover_id")
        enhanced_cover_url = output.get("new_cover_url")
        if enhanced_cover_id:
            enhanced_img = load_image(enhanced_cover_id)
        elif enhanced_cover_url:
            enhanced_img = load_image(enhanced_cover_url)

        if enhanced_img is not None:
            enhanced_path = RESULTS_DIR / f"{idx:02d}_{ctt_id}_enhanced.jpg"
            ok = _save_image(enhanced_img, enhanced_path)
            print(f"[{idx:02d}/{ctt_id}] 保存增强图: {'OK' if ok else 'FAIL'} -> {enhanced_path}")
        else:
            print(f"[{idx:02d}/{ctt_id}] 未获得增强图片或无需增强 (enhanced={output.get('enhanced')})")
    except Exception as e:
        print(f"[{idx:02d}/{ctt_id}] 保存增强图失败: {e}")



def main():
    ensure_results_dir()

    # 初始化模型（使用当前工作目录作为 model_dir）
    model = UserHandler(os.getcwd())

    with CSV_PATH.open("r", encoding="utf-8") as f:
        reader = csv.DictReader(f)
        for idx, row in enumerate(reader, start=1):
            if idx > 20:
                break
            process_one(model, row, idx)


if __name__ == "__main__":
    main()

