#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
类别分布统计脚本
统计tests/top2000.csv中cate4_level1_name字段的分布情况
考虑到同一个ctt_id的类别是固定的，会对ctt_id进行去重处理
"""

import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from collections import Counter
import os

def analyze_category_distribution(csv_file_path):
    """
    分析类别分布情况
    
    Args:
        csv_file_path (str): CSV文件路径
    
    Returns:
        tuple: (去重后的数据, 类别统计结果, 总体统计信息)
    """
    
    # 读取CSV文件
    print(f"正在读取文件: {csv_file_path}")
    df = pd.read_csv(csv_file_path)
    
    print(f"原始数据总行数: {len(df)}")
    print(f"唯一ctt_id数量: {df['ctt_id'].nunique()}")
    
    # 检查是否存在同一ctt_id对应不同类别的情况
    ctt_id_category_check = df.groupby('ctt_id')['cate4_level1_name'].nunique()
    inconsistent_ctt_ids = ctt_id_category_check[ctt_id_category_check > 1]
    
    if len(inconsistent_ctt_ids) > 0:
        print(f"警告: 发现 {len(inconsistent_ctt_ids)} 个ctt_id对应多个不同类别:")
        for ctt_id in inconsistent_ctt_ids.index[:5]:  # 只显示前5个
            categories = df[df['ctt_id'] == ctt_id]['cate4_level1_name'].unique()
            print(f"  {ctt_id}: {list(categories)}")
        if len(inconsistent_ctt_ids) > 5:
            print(f"  ... 还有 {len(inconsistent_ctt_ids) - 5} 个")
    else:
        print("✓ 确认: 每个ctt_id对应的类别都是一致的")
    
    # 按ctt_id去重，保留每个ctt_id的第一条记录
    df_unique = df.drop_duplicates(subset=['ctt_id'], keep='first')
    print(f"去重后数据行数: {len(df_unique)}")
    
    # 统计类别分布
    category_counts = df_unique['cate4_level1_name'].value_counts()
    category_percentages = df_unique['cate4_level1_name'].value_counts(normalize=True) * 100
    
    # 创建统计结果DataFrame
    stats_df = pd.DataFrame({
        '类别': category_counts.index,
        '数量': category_counts.values,
        '占比(%)': category_percentages.values
    })
    
    return df_unique, stats_df, {
        'total_records': len(df),
        'unique_ctt_ids': df['ctt_id'].nunique(),
        'unique_records_after_dedup': len(df_unique),
        'total_categories': len(category_counts),
        'inconsistent_ctt_ids': len(inconsistent_ctt_ids)
    }

def create_visualizations(stats_df, output_dir='tests/stats'):
    """
    创建可视化图表

    Args:
        stats_df (pd.DataFrame): 统计结果DataFrame
        output_dir (str): 输出目录
    """

    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False

    # 1. 柱状图
    plt.figure(figsize=(12, 8))
    bars = plt.bar(range(len(stats_df)), stats_df['数量'])
    plt.xlabel('类别')
    plt.ylabel('数量')
    plt.title('类别分布 - 柱状图')
    plt.xticks(range(len(stats_df)), stats_df['类别'], rotation=45, ha='right')

    # 在柱子上添加百分比标签
    for i, bar in enumerate(bars):
        height = bar.get_height()
        percentage = stats_df.iloc[i]['占比(%)']
        plt.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                f'{percentage:.1f}%', ha='center', va='bottom')

    plt.tight_layout()
    plt.savefig(f'{output_dir}/category_distribution_bar.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 2. 饼图 - 将低于1%的类别合并为"其他"
    plt.figure(figsize=(10, 10))

    # 处理数据：将占比低于1%的类别合并为"其他"
    pie_data = stats_df.copy()
    threshold = 1.0  # 1%阈值

    # 找出占比大于等于1%的类别
    major_categories = pie_data[pie_data['占比(%)'] >= threshold]
    minor_categories = pie_data[pie_data['占比(%)'] < threshold]

    # 如果有小于1%的类别，合并为"其他"
    if len(minor_categories) > 0:
        other_count = minor_categories['数量'].sum()

        # 创建新的数据用于饼图
        pie_categories = major_categories['类别'].tolist() + ['其他']
        pie_counts = major_categories['数量'].tolist() + [other_count]
    else:
        pie_categories = pie_data['类别'].tolist()
        pie_counts = pie_data['数量'].tolist()

    colors = plt.cm.Set3(range(len(pie_categories)))
    plt.pie(pie_counts,
            labels=pie_categories,
            autopct='%1.1f%%',
            colors=colors,
            startangle=90)

    plt.title('类别分布 - 饼图（低于1%归为其他）')
    plt.axis('equal')
    plt.tight_layout()
    plt.savefig(f'{output_dir}/category_distribution_pie.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 3. 水平柱状图（按数量排序）
    plt.figure(figsize=(10, 8))
    sorted_stats = stats_df.sort_values('数量', ascending=True)
    bars = plt.barh(range(len(sorted_stats)), sorted_stats['数量'])
    plt.xlabel('数量')
    plt.ylabel('类别')
    plt.title('类别分布 - 水平柱状图（按数量排序）')
    plt.yticks(range(len(sorted_stats)), sorted_stats['类别'])

    # 在柱子上添加百分比标签
    for i, bar in enumerate(bars):
        width = bar.get_width()
        percentage = sorted_stats.iloc[i]['占比(%)']
        plt.text(width + 0.5, bar.get_y() + bar.get_height()/2.,
                f'{percentage:.1f}%', ha='left', va='center')

    plt.tight_layout()
    plt.savefig(f'{output_dir}/category_distribution_horizontal.png', dpi=300, bbox_inches='tight')
    plt.show()

def save_results(stats_df, summary_info, output_dir='tests/stats'):
    """
    保存统计结果到文件
    
    Args:
        stats_df (pd.DataFrame): 统计结果DataFrame
        summary_info (dict): 总体统计信息
        output_dir (str): 输出目录
    """
    
    os.makedirs(output_dir, exist_ok=True)
    
    # 保存详细统计结果
    stats_df.to_csv(f'{output_dir}/category_distribution_stats.csv', 
                    index=False, encoding='utf-8-sig')
    
    # 保存总结报告
    with open(f'{output_dir}/analysis_summary.txt', 'w', encoding='utf-8') as f:
        f.write("类别分布分析报告\n")
        f.write("=" * 50 + "\n\n")
        
        f.write("数据概览:\n")
        f.write(f"- 原始数据总行数: {summary_info['total_records']}\n")
        f.write(f"- 唯一ctt_id数量: {summary_info['unique_ctt_ids']}\n")
        f.write(f"- 去重后数据行数: {summary_info['unique_records_after_dedup']}\n")
        f.write(f"- 类别总数: {summary_info['total_categories']}\n")
        f.write(f"- 数据不一致的ctt_id数量: {summary_info['inconsistent_ctt_ids']}\n\n")
        
        f.write("类别分布详情:\n")
        f.write("-" * 30 + "\n")
        for _, row in stats_df.iterrows():
            f.write(f"{row['类别']}: {row['数量']} ({row['占比(%)']:.2f}%)\n")

def main():
    """主函数"""
    
    csv_file = 'tests/top2000.csv'
    
    # 检查文件是否存在
    if not os.path.exists(csv_file):
        print(f"错误: 文件 {csv_file} 不存在")
        return
    
    print("开始分析类别分布...")
    print("=" * 50)
    
    # 分析数据
    _, stats_df, summary_info = analyze_category_distribution(csv_file)
    
    # 显示统计结果
    print("\n类别分布统计结果:")
    print("-" * 30)
    print(stats_df.to_string(index=False))
    
    print(f"\n总计: {stats_df['数量'].sum()} 个唯一内容")
    print(f"类别数量: {len(stats_df)} 个")
    
    # 保存结果
    save_results(stats_df, summary_info)
    print(f"\n结果已保存到 tests/stats/ 目录")
    
    # 创建可视化图表
    try:
        create_visualizations(stats_df)
        print("可视化图表已生成并保存")
    except Exception as e:
        print(f"生成可视化图表时出错: {e}")
        print("请确保已安装matplotlib和seaborn: pip install matplotlib seaborn")

if __name__ == "__main__":
    main()
