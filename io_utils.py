from PIL import Image
from io import BytesIO
import requests
from afts import Afts
import time

# 默认配置
endpoint_config = {
    "upload_endpoint_source": "mass.alipay.com",
    "download_endpoint_source": "mdn.alipayobjects.com",
    "authority_endpoint": "mmtcapi.alipay.com"
}

# 创建Afts客户端实例
_afts = Afts(
    biz_key="homepage_cover",      # 业务标识
    biz_secret="cbf990df32e7498197f3633010169426", # 业务密钥
    appid="apwallet",            # 应用id
    endpoint_config=endpoint_config # 环境配置
)

def download_image_from_url(url):
    try:
        # 使用requests获取图片内容
        response = requests.get(url)
        response.raise_for_status()  # 检查请求是否成功
        # 将图片内容返回
        return Image.open(BytesIO(response.content))
    except Exception as e:
        print(f"Error downloading image from {url}: {e}")
        return None


def download_image_from_afts(afts_id):
    try:
        # 使用requests获取图片内容
        content = _afts.download_file(afts_id)
        
        # 将图片内容返回
        return Image.open(BytesIO(content))
    except Exception as e:
        print(f"Error downloading image from {afts_id}: {e}")
        return None
    
def get_url_from_afts(afts_id):
    return _afts.get_url(afts_id)
    
def norm_url(file_or_url):
    if 'http' in file_or_url:
        return file_or_url
    elif '.png' in file_or_url or '.jpg' in file_or_url:
        return file_or_url
    else:
        return _afts.get_url(file_or_url)

def load_image(file_or_url):
    if not isinstance(file_or_url, str):
        file_or_url = str(file_or_url)

    if 'http' in file_or_url:
        return download_image_from_url(file_or_url)
    elif '.png' in file_or_url or '.jpg' in file_or_url:
        return Image.open(file_or_url)
    else:
        return download_image_from_afts(file_or_url)