#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
对tests/top2000.csv进行画质优化测试的脚本
使用GenericAPICaller调用服务，支持并发处理和进度条显示
"""

import json
import time
import random
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
from tqdm import tqdm
import pandas as pd

# 导入现有的API服务模块
from api_services import GenericAPICaller

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("CoverEnhanceTest")


def smart_cover_enhancement(data):
    """
    调用智能封面增强服务（使用通用调用器）
    """
    caller = GenericAPICaller(
        scene_name="smart_cover_enhancement",
        post_url="https://paiplusinference.alipay.com/inference/7a2f7401cee56342_smart_cover_enhancement/v1",
        input_key="data",
        output_key="output",
        output_json_parse=True,  # 输出需要解析为JSON
    )
    timeout = data.get("timeout", 30000)
    return caller.call(data, timeout=timeout)


def process_single_row(row_data):
    """处理单行数据"""
    content_id = row_data['content_id']
    title = row_data['title']
    cat = row_data['cate_l1']

    # 处理封面ID的逻辑：优先使用optimize_aggregation_new_cover，如果为空则使用cover_static
    optimize_cover = row_data.get('optimize_aggregation_new_cover')
    static_cover = row_data.get('cover_static')

    # 判断使用哪个封面ID
    if pd.notna(optimize_cover) and str(optimize_cover).strip():
        smart_cover_id = optimize_cover
        cover_source = "optimized"
    elif pd.notna(static_cover) and str(static_cover).strip():
        smart_cover_id = static_cover
        cover_source = "original"
    else:
        # 两个字段都为空，跳过处理
        return None, f"Content {content_id}: Both optimize_aggregation_new_cover and cover_static are empty, skipping"

    try:
        # 构造输入数据
        input_data = {
            "cover_id": smart_cover_id,
            "cover_url": "",
            "title": title,
            "cat": cat,
            "video_name": "",
            "video_id": "",
            "contentId": content_id,
            "logs": {},
            "version": 1,
            "mode": "post"  # 使用maya模式
        }

        # 调用增强服务
        result = smart_cover_enhancement(input_data)

        # 构造输出行
        output_row = {
            'contentid': content_id,
            'aftsid': '',  # 默认为空，只有增强成功才填入新的cover_id
            'subVersion': 4, # 色调优化版本号
            'bizType': 'home_page_aigc',
            'type': 'cover',
            'dt': '20250821',
            'cover_source': cover_source  # 添加封面来源说明
        }

        # 如果增强成功，使用新的cover_id
        if isinstance(result, dict) and 'new_cover_id' in result and result['new_cover_id']:
            output_row['aftsid'] = result['new_cover_id']

        return output_row, None

    except Exception as e:
        error_msg = f"Error processing {content_id}: {str(e)}"
        logger.error(error_msg)
        return None, error_msg


def main():
    """主函数"""
    # 读取CSV文件
    input_file = "tests/3_top2k_with_results.csv"
    output_file = "tests/3_top2k_with_results_enhanced.csv"
    
    logger.info(f"Reading input file: {input_file}")
    
    try:
        df = pd.read_csv(input_file)
        logger.info(f"Loaded {len(df)} rows from CSV")
    except Exception as e:
        logger.error(f"Failed to read CSV file: {e}")
        return
    
    # 准备结果列表
    results = []
    errors = []
    skipped = []

    # 并发处理配置
    max_workers = 8  # 可以根据需要调整并发数
    # # 索引第20-30行
    # df = df.iloc[25:35]

    logger.info(f"Starting processing with {max_workers} workers...")

    # 使用ThreadPoolExecutor进行并发处理
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务
        future_to_row = {
            executor.submit(process_single_row, row.to_dict()): idx
            for idx, row in df.iterrows()
        }
        
        # 使用tqdm显示进度条
        with tqdm(total=len(future_to_row), desc="Processing covers") as pbar:
            for future in as_completed(future_to_row):
                row_idx = future_to_row[future]
                try:
                    result, error = future.result()
                    if result:
                        results.append(result)
                    if error:
                        if "skipping" in error:
                            skipped.append(f"Row {row_idx}: {error}")
                        else:
                            errors.append(f"Row {row_idx}: {error}")
                except Exception as e:
                    error_msg = f"Row {row_idx}: Exception in future: {str(e)}"
                    logger.error(error_msg)
                    errors.append(error_msg)
                
                pbar.update(1)
    
    # 保存结果
    if results:
        result_df = pd.DataFrame(results)
        result_df.to_csv(output_file, index=False)
        logger.info(f"Saved {len(results)} results to {output_file}")
    else:
        logger.warning("No results to save")
    
    # 报告错误和跳过的记录
    if errors:
        error_file = "tests/top2000_enhancement_errors.log"
        with open(error_file, 'w', encoding='utf-8') as f:
            for error in errors:
                f.write(f"{error}\n")
        logger.warning(f"Encountered {len(errors)} errors, saved to {error_file}")

    if skipped:
        skip_file = "tests/top2000_enhancement_skipped.log"
        with open(skip_file, 'w', encoding='utf-8') as f:
            for skip in skipped:
                f.write(f"{skip}\n")
        logger.info(f"Skipped {len(skipped)} rows (empty cover fields), saved to {skip_file}")

    logger.info("Processing completed!")
    print(f"\nSummary:")
    print(f"- Total rows processed: {len(df)}")
    print(f"- Successful results: {len(results)}")
    print(f"- Skipped rows (empty covers): {len(skipped)}")
    print(f"- Errors: {len(errors)}")
    print(f"- Output file: {output_file}")
    if errors:
        print(f"- Error log: tests/top2000_enhancement_errors.log")
    if skipped:
        print(f"- Skip log: tests/top2000_enhancement_skipped.log")


if __name__ == "__main__":
    main()
