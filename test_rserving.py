from rserving_client.rserving_client import (
    ArksConfig,
    ArksRequest,
    MayaClient
)
import json

# modify config if need
# config = ArksConfig()
# client = MayaClient.get_instance(config)
client = MayaClient.get_instance()
request = ArksRequest()
request.request_timeout = 60000
request.read_timeout = 10000 
request.connect_timeout = 10000

request.scene_name = "main_recognition_vlm"
request.chain_name = "v1"
request.session_id = "traceId"
request.set_lbconfig({"arks.antvip.arrow.cross.city": "true"})
request.set_item_num(1)
item_id = "testId"
item = request.add_item(item_id)
tensor_key = "input_args"
shapes = [1]
tensor_values = [json.dumps({"cover_url":"https://mass.alipay.com/afts/file/MVYhSqfz1G4AAAAAgDAAAAgAennEAQBr?bizType=video_generation&token=trVYq6JrCGMCnthd13sCiZSF64QOUHc4ZfTYggxNhN0DAAAAZAAAxHloqGiC","media_type":"video"})]
item.add_tensor_feature(tensor_key, shapes, tensor_values)
'''
FE除外，需要用
item.set_features({"key": "value"})
'''
res = client.call(request)
if res.success:
    print("request success")
    # print(res.items[0].item_id)
    print(res.items[0].attributes)
else:
    print("request fail")
    print(res.error_msg)